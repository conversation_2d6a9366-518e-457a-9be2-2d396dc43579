# OpenRouteService (ORS) Local Docker Setup

This guide provides step-by-step instructions for running OpenRouteService locally using Docker with India map data from OpenStreetMap.

## 🚀 Quick Start

### Prerequisites

- Docker and Docker Compose installed
- At least 12GB RAM available for processing India OSM data
- At least 50GB free disk space

### 1. <PERSON><PERSON> and Setup

```bash
git clone <your-repository>
cd openrouteservice
```

### 2. Start the Service

```bash
# Start ORS in Docker
docker-compose up -d

# Monitor logs (optional)
docker-compose logs -f ors-app
```

### 3. Wait for Initialization

The first startup will take 30-60 minutes as it builds routing graphs from the India OSM data (~2GB).

### 4. Test the Service

```bash
# Check health
curl -X GET "http://localhost:8080/ors/v2/health"

# Test routing (Heidelberg coordinates for demo)
curl -X POST "http://localhost:8080/ors/v2/directions/driving-car" \
  -H "Content-Type: application/json" \
  -d '{"coordinates": [[8.681495, 49.41461], [8.687872, 49.420318]]}'
```

## 📁 Project Structure

```
openrouteservice/
├── docker-compose.yml          # Docker Compose configuration
├── custom-ors-config.yml       # Custom ORS configuration
├── ors-config.yml              # Main ORS configuration
├── ors-docker/
│   └── files/
│       └── india-latest.osm.pbf # India OSM data
└── LOCAL_SETUP_README.md       # This file
```

## ⚙️ Configuration

### Key Features

- **Maximum Distance**: 5000km routing distance limit
- **Memory Optimized**: 24GB heap size for large datasets
- **Elevation Disabled**: Optimized for 2D routing
- **India Data**: Full India OpenStreetMap coverage

### Profiles Enabled

- ✅ **driving-car**: Car routing with 5000km limit
- ⚠️ **walking**: Disabled (can be enabled)
- ⚠️ **cycling-regular**: Disabled (can be enabled)

## 🔧 Management Commands

### Start/Stop Service

```bash
# Start service
docker-compose up -d

# Stop service
docker-compose down

# Restart service
docker-compose restart ors-app

# Stop and remove volumes (full reset)
docker-compose down -v
```

### Monitoring

```bash
# View logs
docker-compose logs -f ors-app

# Check container status
docker-compose ps

# Check resource usage
docker stats ors-app
```

## 🌐 API Usage

### Health Check

```bash
curl -X GET "http://localhost:8080/ors/v2/health"
```

### Routing Request

```bash
curl -X POST "http://localhost:8080/ors/v2/directions/driving-car" \
  -H "Content-Type: application/json" \
  -d '{
    "coordinates": [
      [77.5946, 12.9716],  # Bangalore
      [78.4867, 17.3850]   # Hyderabad
    ]
  }'
```

### Response Format

```json
{
  "bbox": [77.5946, 12.9716, 78.4867, 17.3850],
  "routes": [{
    "summary": {
      "distance": 574832.1,
      "duration": 20539.8
    },
    "segments": [...],
    "geometry": "...",
    "way_points": [0, 142]
  }],
  "metadata": {
    "attribution": "openrouteservice.org, OpenStreetMap contributors",
    "service": "routing",
    "timestamp": *************,
    "query": {...},
    "engine": {...}
  }
}
```

## 🛠️ Troubleshooting

### Common Issues

1. **OutOfMemoryError**
   ```bash
   # Increase memory in docker-compose.yml
   JAVA_HEAP_MAX: "32g"
   memory: 40g
   ```

2. **Service Not Starting**
   ```bash
   # Check logs
   docker-compose logs ors-app
   
   # Reset everything
   docker-compose down -v
   docker-compose up -d
   ```

3. **Port Already in Use**
   ```bash
   # Check what's using port 8080
   lsof -i :8080
   
   # Kill the process
   kill <PID>
   ```

### Performance Tuning

- **Memory**: Adjust `JAVA_HEAP_MAX` based on available RAM
- **Profiles**: Disable unused profiles to save memory
- **Data**: Use smaller OSM extracts for testing

## 📊 System Requirements

### Minimum Requirements

- **RAM**: 16GB (32GB recommended)
- **Storage**: 20GB free space
- **CPU**: 4 cores (8 cores recommended)

### Production Requirements

- **RAM**: 32GB+
- **Storage**: 100GB+ SSD
- **CPU**: 8+ cores
- **Network**: Stable internet for OSM data download

## 🔄 Updating OSM Data

1. Download new OSM data to `ors-docker/files/`
2. Update `OSM_FILE` in `docker-compose.yml`
3. Set `REBUILD_GRAPHS: "True"`
4. Restart: `docker-compose down -v && docker-compose up -d`

## 📝 Configuration Details

### Maximum Distance Configuration

The service is configured with a 5000km maximum routing distance in `custom-ors-config.yml`:

```yaml
ors:
  engine:
    profiles:
      driving-car:
        service:
          maximum_distance: 5000000  # 5000km in meters
```

### Memory Configuration

Docker Compose is configured with optimized memory settings:

```yaml
environment:
  JAVA_HEAP_MIN: "16g"
  JAVA_HEAP_MAX: "24g"
deploy:
  resources:
    limits:
      memory: 32g
```

## 🆘 Support

For issues and questions:
1. Check the logs: `docker-compose logs ors-app`
2. Verify configuration files
3. Ensure sufficient system resources
4. Check OpenRouteService documentation

---

**Status**: ✅ Service Running | **Port**: 8080 | **Max Distance**: 5000km
